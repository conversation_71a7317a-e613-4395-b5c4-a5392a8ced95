"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CLIService = void 0;
const engine_1 = require("../../core/engine");
const tools_1 = require("../../core/tools");
const AuthManager_1 = require("../components/auth/AuthManager");
const TerminalManager_1 = require("../components/terminal/TerminalManager");
const ConfigComponent_1 = require("../components/config/ConfigComponent");
const HistoryComponent_1 = require("../components/history/HistoryComponent");
class CLIService {
    state;
    config;
    configManager;
    aiEngine;
    components = new Map();
    toolConfirmationHandler;
    constructor(configManager) {
        this.configManager = configManager;
        // Initialize default state
        this.state = {
            currentView: 'auth',
            isAuthenticated: false,
            currentProvider: '',
            currentModel: '',
        };
        // Initialize default config
        this.config = {
            theme: {
                primary: 'cyan',
                secondary: 'blue',
                success: 'green',
                error: 'red',
                warning: 'yellow',
                info: 'blue',
                muted: 'gray',
                accent: 'magenta',
                background: 'black',
                text: 'white',
            },
            terminal: {
                prompt: '🤖 AI CLI',
                maxHistorySize: 1000,
                autoSave: true,
                showTimestamps: true,
                showTokenCounts: true,
                streamResponses: true,
            },
            ui: {
                animations: true,
                sounds: false,
                compactMode: false,
                showBanner: true,
            },
            showTimestamps: true,
            showTokenCounts: true,
        };
        // Initialize AI engine with tool registry
        const toolRegistry = (0, tools_1.createDefaultToolRegistry)();
        this.toolConfirmationHandler = {
            confirmExecution: async (toolName, parameters) => {
                const terminalManager = this.components.get('terminal');
                if (terminalManager) {
                    return await terminalManager.confirmToolExecution(toolName, parameters);
                }
                return false;
            },
            confirmToolExecution: async (toolName, parameters) => {
                const terminalManager = this.components.get('terminal');
                if (terminalManager) {
                    return await terminalManager.confirmToolExecution(toolName, parameters);
                }
                return false;
            }
        };
        this.aiEngine = new engine_1.AIEngine(configManager, toolRegistry, this.toolConfirmationHandler);
        this.initializeComponents();
    }
    initializeComponents() {
        // Initialize all CLI components
        this.components.set('auth', new AuthManager_1.AuthManager(this.state, this.config, this.configManager));
        this.components.set('terminal', new TerminalManager_1.TerminalManager(this.state, this.config, this.configManager));
        this.components.set('config', new ConfigComponent_1.ConfigComponent(this.state, this.config, this.configManager));
        this.components.set('history', new HistoryComponent_1.HistoryComponent(this.state, this.config, this.configManager));
    }
    async start() {
        // Check if any providers are configured
        const configuredProviders = this.configManager.getConfiguredProviders();
        if (configuredProviders.length === 0) {
            // No providers configured, start with auth
            this.state.currentView = 'auth';
            this.state.isAuthenticated = false;
        }
        else {
            // Providers configured, check if we can authenticate
            const defaultProvider = this.configManager.getDefaultProvider();
            if (defaultProvider && this.configManager.isProviderConfigured(defaultProvider)) {
                this.state.currentProvider = defaultProvider;
                this.state.currentModel = this.configManager.getProviderConfig(defaultProvider).defaultModel;
                this.state.isAuthenticated = true;
                this.state.currentView = 'terminal';
            }
            else {
                this.state.currentView = 'auth';
                this.state.isAuthenticated = false;
            }
        }
        await this.renderCurrentView();
    }
    async renderCurrentView() {
        const component = this.components.get(this.state.currentView);
        if (component) {
            await component.render();
        }
        else {
            throw new Error(`Unknown view: ${this.state.currentView}`);
        }
    }
    async switchView(view) {
        // Cleanup current component
        const currentComponent = this.components.get(this.state.currentView);
        if (currentComponent && currentComponent.cleanup) {
            await currentComponent.cleanup();
        }
        // Update state
        this.state.currentView = view;
        // Render new view
        await this.renderCurrentView();
    }
    async handleInput(input) {
        const component = this.components.get(this.state.currentView);
        if (component && component.handleInput) {
            await component.handleInput(input);
        }
    }
    getState() {
        return { ...this.state };
    }
    updateState(updates) {
        Object.assign(this.state, updates);
    }
    getConfig() {
        return { ...this.config };
    }
    updateConfig(updates) {
        Object.assign(this.config, updates);
    }
    getAIEngine() {
        return this.aiEngine;
    }
    getConfigManager() {
        return this.configManager;
    }
    async shutdown() {
        // Cleanup all components
        for (const [name, component] of this.components) {
            if (component.cleanup) {
                try {
                    await component.cleanup();
                }
                catch (error) {
                    console.error(`Error cleaning up component ${name}:`, error);
                }
            }
        }
        // Clear components
        this.components.clear();
    }
    // Navigation helpers
    async goToAuth() {
        await this.switchView('auth');
    }
    async goToTerminal() {
        if (!this.state.isAuthenticated) {
            throw new Error('Must be authenticated to access terminal');
        }
        await this.switchView('terminal');
    }
    async goToConfig() {
        await this.switchView('config');
    }
    async goToHistory() {
        await this.switchView('history');
    }
    // Authentication helpers
    async authenticate(provider, model) {
        this.state.isAuthenticated = true;
        this.state.currentProvider = provider;
        this.state.currentModel = model;
    }
    async logout() {
        this.state.isAuthenticated = false;
        this.state.currentProvider = '';
        this.state.currentModel = '';
        this.state.conversationId = undefined;
        await this.switchView('auth');
    }
    // Provider management
    async switchProvider(provider) {
        if (!this.configManager.isProviderConfigured(provider)) {
            throw new Error(`Provider ${provider} is not configured`);
        }
        const config = this.configManager.getProviderConfig(provider);
        this.state.currentProvider = provider;
        this.state.currentModel = config.defaultModel;
        await this.configManager.setDefaultProvider(provider);
    }
    async switchModel(model) {
        this.state.currentModel = model;
    }
    // Conversation management
    async startNewConversation() {
        this.state.conversationId = undefined;
        // Reset terminal if we're in terminal view
        if (this.state.currentView === 'terminal') {
            const terminalManager = this.components.get('terminal');
            if (terminalManager) {
                await terminalManager.startNewConversation();
            }
        }
    }
    async loadConversation(conversationId) {
        this.state.conversationId = conversationId;
        // Load conversation in terminal if we're in terminal view
        if (this.state.currentView === 'terminal') {
            const terminalManager = this.components.get('terminal');
            if (terminalManager) {
                await terminalManager.loadConversation(conversationId);
            }
        }
    }
    // Error handling
    async handleError(error) {
        console.error('CLI Service Error:', error);
        // Show error to user through current component
        const component = this.components.get(this.state.currentView);
        if (component && 'showError' in component) {
            await component.showError(error.message);
        }
        else {
            console.error('Error:', error.message);
        }
    }
    // Utility methods
    isAuthenticated() {
        return this.state.isAuthenticated;
    }
    getCurrentProvider() {
        return this.state.currentProvider;
    }
    getCurrentModel() {
        return this.state.currentModel;
    }
    getCurrentView() {
        return this.state.currentView;
    }
}
exports.CLIService = CLIService;
//# sourceMappingURL=CLIService.js.map