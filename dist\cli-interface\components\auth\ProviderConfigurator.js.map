{"version": 3, "file": "ProviderConfigurator.js", "sourceRoot": "", "sources": ["../../../../src/cli-interface/components/auth/ProviderConfigurator.ts"], "names": [], "mappings": ";;;;;;AAAA,wDAAgC;AAChC,2DAAwD;AAExD,+CAA0D;AAG1D,MAAa,oBAAqB,SAAQ,6BAAa;IAC7C,aAAa,CAAgB;IAErC,YAAY,KAAe,EAAE,MAAiB,EAAE,aAA4B;QAC1E,KAAK,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;QACrB,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;IACrC,CAAC;IAEM,KAAK,CAAC,MAAM;QACjB,6DAA6D;IAC/D,CAAC;IAEM,KAAK,CAAC,iBAAiB,CAAC,YAAoB;QACjD,MAAM,QAAQ,GAAG,2BAAmB,CAAC,YAAY,CAAC,CAAC;QACnD,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,MAAM,IAAI,CAAC,SAAS,CAAC,qBAAqB,YAAY,EAAE,CAAC,CAAC;YAC1D,OAAO,KAAK,CAAC;QACf,CAAC;QAED,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,kBAAkB,QAAQ,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;QAE/E,IAAI,CAAC;YACH,MAAM,cAAc,GAAG,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC;YAC1E,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,2BAA2B,CAAC,QAAQ,EAAE,cAAc,CAAC,CAAC;YAEvF,IAAI,CAAC,aAAa,EAAE,CAAC;gBACnB,OAAO,KAAK,CAAC,CAAC,iBAAiB;YACjC,CAAC;YAED,MAAM,IAAI,CAAC,yBAAyB,CAAC,YAAY,EAAE,aAAa,CAAC,CAAC;YAClE,MAAM,IAAI,CAAC,wBAAwB,CAAC,YAAY,CAAC,CAAC;YAElD,MAAM,IAAI,CAAC,WAAW,CAAC,GAAG,QAAQ,CAAC,WAAW,2BAA2B,CAAC,CAAC;YAC3E,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,CAAC,SAAS,CAAC,iCAAiC,KAAK,EAAE,CAAC,CAAC;YAC/D,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,2BAA2B,CAAC,QAAa,EAAE,cAAoB;QAC3E,MAAM,SAAS,GAAU;YACvB;gBACE,IAAI,EAAE,UAAU;gBAChB,IAAI,EAAE,QAAQ;gBACd,OAAO,EAAE,cAAc,QAAQ,CAAC,WAAW,WAAW;gBACtD,OAAO,EAAE,cAAc,EAAE,MAAM;gBAC/B,QAAQ,EAAE,CAAC,KAAa,EAAE,EAAE;oBAC1B,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;wBACxC,OAAO,qBAAqB,CAAC;oBAC/B,CAAC;oBACD,OAAO,IAAI,CAAC;gBACd,CAAC;gBACD,IAAI,EAAE,GAAG;aACV;YACD;gBACE,IAAI,EAAE,MAAM;gBACZ,IAAI,EAAE,cAAc;gBACpB,OAAO,EAAE,uBAAuB;gBAChC,OAAO,EAAE,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,KAAa,EAAE,EAAE,CAAC,CAAC;oBAC/C,IAAI,EAAE,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC;oBACnC,KAAK,EAAE,KAAK;iBACb,CAAC,CAAC;gBACH,OAAO,EAAE,cAAc,EAAE,YAAY,IAAI,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;aAC5D;SACF,CAAC;QAEF,2DAA2D;QAC3D,IAAI,QAAQ,CAAC,OAAO,EAAE,CAAC;YACrB,SAAS,CAAC,IAAI,CAAC;gBACb,IAAI,EAAE,OAAO;gBACb,IAAI,EAAE,SAAS;gBACf,OAAO,EAAE,sBAAsB;gBAC/B,OAAO,EAAE,cAAc,EAAE,OAAO,IAAI,QAAQ,CAAC,OAAO;gBACpD,QAAQ,EAAE,CAAC,KAAa,EAAE,EAAE;oBAC1B,IAAI,KAAK,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE,CAAC;wBACrC,OAAO,0BAA0B,CAAC;oBACpC,CAAC;oBACD,OAAO,IAAI,CAAC;gBACd,CAAC;aACF,CAAC,CAAC;QACL,CAAC;QAED,oCAAoC;QACpC,SAAS,CAAC,IAAI,CAAC;YACb,IAAI,EAAE,SAAS;YACf,IAAI,EAAE,mBAAmB;YACzB,OAAO,EAAE,8BAA8B;YACvC,OAAO,EAAE,KAAK;SACf,CAAC,CAAC;QAEH,MAAM,OAAO,GAAG,MAAM,kBAAQ,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;QAEjD,IAAI,OAAO,CAAC,iBAAiB,EAAE,CAAC;YAC9B,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,2BAA2B,EAAE,CAAC;YAChE,MAAM,CAAC,MAAM,CAAC,OAAO,EAAE,cAAc,CAAC,CAAC;QACzC,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAEO,KAAK,CAAC,2BAA2B;QACvC,OAAO,MAAM,kBAAQ,CAAC,MAAM,CAAC;YAC3B;gBACE,IAAI,EAAE,QAAQ;gBACd,IAAI,EAAE,WAAW;gBACjB,OAAO,EAAE,4BAA4B;gBACrC,QAAQ,EAAE,CAAC,KAAa,EAAE,EAAE;oBAC1B,IAAI,KAAK,IAAI,CAAC,KAAK,GAAG,CAAC,IAAI,KAAK,GAAG,MAAM,CAAC,EAAE,CAAC;wBAC3C,OAAO,yCAAyC,CAAC;oBACnD,CAAC;oBACD,OAAO,IAAI,CAAC;gBACd,CAAC;aACF;YACD;gBACE,IAAI,EAAE,QAAQ;gBACd,IAAI,EAAE,aAAa;gBACnB,OAAO,EAAE,oCAAoC;gBAC7C,QAAQ,EAAE,CAAC,KAAa,EAAE,EAAE;oBAC1B,IAAI,KAAK,IAAI,CAAC,KAAK,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE,CAAC;wBACtC,OAAO,yCAAyC,CAAC;oBACnD,CAAC;oBACD,OAAO,IAAI,CAAC;gBACd,CAAC;aACF;YACD;gBACE,IAAI,EAAE,OAAO;gBACb,IAAI,EAAE,cAAc;gBACpB,OAAO,EAAE,kCAAkC;aAC5C;SACF,CAAC,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,yBAAyB,CAAC,YAAoB,EAAE,aAAkB;QAC9E,MAAM,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,YAAY,EAAE;YACjD,MAAM,EAAE,aAAa,CAAC,MAAM;YAC5B,YAAY,EAAE,aAAa,CAAC,YAAY;YACxC,OAAO,EAAE,aAAa,CAAC,OAAO;YAC9B,SAAS,EAAE,aAAa,CAAC,SAAS;YAClC,WAAW,EAAE,aAAa,CAAC,WAAW;YACtC,OAAO,EAAE,IAAI;YACb,QAAQ,EAAE,aAAa,CAAC,QAAQ,IAAI,EAAE;SACvC,CAAC,CAAC;QAEH,0CAA0C;QAC1C,IAAI,aAAa,CAAC,SAAS,IAAI,aAAa,CAAC,WAAW,IAAI,aAAa,CAAC,YAAY,EAAE,CAAC;YACvF,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,CAAC;YAC9C,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC,QAAQ,EAAE,CAAC;gBAC7C,MAAM,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC,QAAQ,GAAG,EAAE,CAAC;YAC/C,CAAC;YAED,IAAI,aAAa,CAAC,SAAS,EAAE,CAAC;gBAC5B,MAAM,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC,QAAQ,CAAC,SAAS,GAAG,aAAa,CAAC,SAAS,CAAC;YAC9E,CAAC;YACD,IAAI,aAAa,CAAC,WAAW,EAAE,CAAC;gBAC9B,MAAM,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC,QAAQ,CAAC,WAAW,GAAG,aAAa,CAAC,WAAW,CAAC;YAClF,CAAC;YACD,IAAI,aAAa,CAAC,YAAY,EAAE,CAAC;gBAC/B,MAAM,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC,QAAQ,CAAC,YAAY,GAAG,aAAa,CAAC,YAAY,CAAC;YACpF,CAAC;YAED,MAAM,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE,CAAC;QACxC,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,wBAAwB,CAAC,YAAoB;QACzD,MAAM,mBAAmB,GAAG,IAAI,CAAC,aAAa,CAAC,sBAAsB,EAAE,CAAC;QAExE,4CAA4C;QAC5C,IAAI,mBAAmB,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACrC,MAAM,IAAI,CAAC,aAAa,CAAC,kBAAkB,CAAC,YAAY,CAAC,CAAC;YAC1D,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,OAAO,2BAAmB,CAAC,YAAY,CAAC,CAAC,WAAW,sBAAsB,CAAC,CAAC;QAClG,CAAC;IACH,CAAC;IAEM,KAAK,CAAC,sBAAsB,CAAC,YAAoB;QACtD,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,yBAAyB,2BAAmB,CAAC,YAAY,CAAC,CAAC,WAAW,KAAK,CAAC,CAAC;QAErG,IAAI,CAAC;YACH,yCAAyC;YACzC,0EAA0E;YAC1E,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,gBAAgB;YAEzE,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,EAAE,4BAA4B,CAAC,CAAC;YAC3D,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,KAAK,EAAE,2BAA2B,KAAK,EAAE,CAAC,CAAC;YAClE,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAEO,iBAAiB,CAAC,KAAa;QACrC,sDAAsD;QACtD,MAAM,iBAAiB,GAA2B;YAChD,OAAO,EAAE,sCAAsC;YAC/C,aAAa,EAAE,gCAAgC;YAC/C,eAAe,EAAE,2CAA2C;YAC5D,wBAAwB,EAAE,2BAA2B;YACrD,0BAA0B,EAAE,gCAAgC;YAC5D,yBAAyB,EAAE,sBAAsB;YACjD,YAAY,EAAE,8BAA8B;YAC5C,mBAAmB,EAAE,0BAA0B;YAC/C,eAAe,EAAE,4BAA4B;YAC7C,gBAAgB,EAAE,8BAA8B;SACjD,CAAC;QAEF,MAAM,WAAW,GAAG,iBAAiB,CAAC,KAAK,CAAC,CAAC;QAC7C,OAAO,WAAW,CAAC,CAAC,CAAC,GAAG,KAAK,MAAM,WAAW,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC;IAC3D,CAAC;IAEO,UAAU,CAAC,GAAW;QAC5B,IAAI,CAAC;YACH,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC;YACb,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,MAAM,CAAC;YACP,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;CACF;AA1ND,oDA0NC"}